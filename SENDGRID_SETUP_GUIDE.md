# 📧 SendGrid Email Setup Guide for LawFort

## 🚀 Quick Setup Steps

### 1. Install SendGrid Package
```bash
cd Backend
pip install sendgrid==6.10.0
```

### 2. Run Database Migration
```bash
# Windows
python migrate_oauth_email.py

# If you get the /usr/bin/env error, just run:
python migrate_oauth_email.py
```

### 3. Create SendGrid Account
1. Go to [SendGrid.com](https://sendgrid.com)
2. Sign up for a free account (100 emails/day free)
3. Verify your email address

### 4. Generate API Key
1. Login to SendGrid dashboard
2. Go to **Settings** → **API Keys**
3. Click **Create API Key**
4. Choose **Restricted Access**
5. Give it a name like "LawFort Email System"
6. Under **Mail Send**, select **Full Access**
7. Click **Create & View**
8. **Copy the API key** (you won't see it again!)

### 5. Verify Sender Email
1. Go to **Settings** → **Sender Authentication**
2. Click **Verify a Single Sender**
3. Fill in your details:
   - **From Name**: LawFort
   - **From Email**: <EMAIL> (or use your personal email for testing)
   - **Reply To**: same as from email
   - **Company**: LawFort
   - **Address**: Your address
4. Click **Create**
5. Check your email and click the verification link

### 6. Configure Environment Variables
Update your `.env` file in the Backend directory:

```env
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME=LawFort

# Flask Configuration
SECRET_KEY=your_secret_key_here

# SendGrid Email Configuration
SENDGRID_API_KEY=SG.your_actual_api_key_here
FROM_EMAIL=<EMAIL>
```

**Important**: Replace with your actual values:
- `SG.your_actual_api_key_here` → Your actual SendGrid API key
- `<EMAIL>` → Your verified sender email

### 7. Test the Setup
```bash
# Start backend
cd Backend
python app.py

# Start frontend (new terminal)
npm run dev
```

## 🧪 Testing Email System

1. **Login as Admin**
   - Use an admin account or create one in database

2. **Access Email Management**
   - Go to Admin Dashboard → Email Management tab

3. **Send Test Email**
   - Click "Compose Email"
   - Select test users
   - Enter subject: "Test Email from LawFort"
   - Enter content: "This is a test email using SendGrid!"
   - Click "Send Email"

4. **Verify Delivery**
   - Check recipient's email inbox
   - Check SendGrid dashboard for delivery stats

## 🔧 Troubleshooting

### Issue: "SendGrid API key not configured"
**Solution**: Make sure your `.env` file has the correct `SENDGRID_API_KEY`

### Issue: "Unauthorized" error
**Solutions**:
- Check if API key is correct
- Ensure API key has "Mail Send" permissions
- Regenerate API key if needed

### Issue: "The from address does not match a verified Sender Identity"
**Solutions**:
- Verify your sender email in SendGrid dashboard
- Make sure `FROM_EMAIL` in `.env` matches verified email
- Wait a few minutes after verification

### Issue: Emails not received
**Solutions**:
- Check spam/junk folder
- Verify recipient email addresses
- Check SendGrid Activity dashboard for delivery status

## 📊 SendGrid Dashboard Features

After setup, you can monitor:
- **Activity** → See all sent emails and their status
- **Statistics** → View delivery rates, opens, clicks
- **Suppressions** → Manage bounced/blocked emails

## 💰 SendGrid Pricing

- **Free Tier**: 100 emails/day forever
- **Essentials**: $14.95/month for 50,000 emails
- **Pro**: $89.95/month for 100,000 emails

The free tier is perfect for development and small-scale testing!

## 🔒 Security Best Practices

1. **Never commit API keys** to version control
2. **Use environment variables** for sensitive data
3. **Restrict API key permissions** to only what's needed
4. **Rotate API keys** periodically
5. **Monitor usage** in SendGrid dashboard

## ✅ Verification Checklist

- [ ] SendGrid account created and verified
- [ ] API key generated with Mail Send permissions
- [ ] Sender email verified in SendGrid
- [ ] `.env` file configured with correct values
- [ ] Database migration completed
- [ ] Backend starts without errors
- [ ] Can access email management in admin dashboard
- [ ] Test email sends successfully
- [ ] Email received in recipient's inbox

Once all items are checked, your SendGrid email system is ready! 🎉
